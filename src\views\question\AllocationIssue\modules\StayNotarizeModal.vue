<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    @cancel="handleCancel"
    wrapClassName="ant-modal-cust-warp"
    style="height: 70%; overflow: hidden; overflow-y: auto"
    :centered="true"
  >
    <div v-if="model != null">
      <a-row>
        <a-col>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="问题类型">
            <!-- <a-input :readonly="true" v-model="model.questionType" /> -->
            <j-dict-select-tag
              type="list"
              :disabled="disabled"
              v-model="model.questionType"
              :trigger-change="false"
              dictCode="helpQuestionType"
              placeholder="请输入问题类型"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="问题内容">
            <textarea :disabled="disabled" style="width: 100%" v-model="model.question"></textarea>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="提问人">
            <a-input :disabled="disabled" v-model="model.quizzer" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="联系电话">
            <a-input :disabled="disabled" v-model="model.contact" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="地区">
            <a-input :disabled="disabled" v-model="model.regionName" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="提问时间">
            <a-input :disabled="disabled" v-model="model.createTime" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="设备地址">
            <a-input :disabled="disabled" v-model="model.ip" />
          </a-form-item>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-popconfirm placement="bottom" okText="提交" cancelText="取消" @confirm="confirm" style="padding-bottom: 0">
        <a-icon slot="icon" type="" style="width: 0px" />
        <template slot="title">
          <textarea
            v-model="answererContent"
            placeholder="请输入答复"
            style="width: 300px; resize: none; height: 95px; border: 1px solid #d9d9d9; margin-left: -21px"
          >
          </textarea>
        </template>
        <a-button>关闭问题</a-button>
      </a-popconfirm>
      <a-button @click="handleCancel">关闭窗口</a-button>
      <a-button type="primary" :confirmLoading="confirmLoading" @click="handleOk"> 确定问题 </a-button>
    </template>
  </a-modal>
</template>
<script>
import { formatDate } from '@/utils/util'
import { editRecord } from '@api/AllocationIssue'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import { getAction } from '@api/manage'
export default {
  name: 'StayNotarizeModal', //确认工单弹窗
  components: {
    JDictSelectTag,
  },
  data() {
    return {
      title: '操作',
      visible: false,
      disabled: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '700px',
      model: null,
      answererContent: '', //答复内容
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      that.confirmLoading = true
      getAction("/question/question/start",{questionId:this.model.id})
        .then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.$emit('ok')
          } else {
            that.$message.warning(res.message)
          }
        })
        .finally(() => {
          that.confirmLoading = false
          that.close()
        })
    },
    handleCancel() {
      this.close()
    },

    //关闭问题提交确认
    confirm() {
      const that = this
      that.confirmLoading = true
      getAction('/question/question/closeQuestion', {questionId:this.model.id,content:that.answererContent}).then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.$emit('ok')
          } else {
            that.$message.warning(res.message)
          }
        })
        .finally(() => {
          that.confirmLoading = false
          that.close()
        })
    },
  },
}
</script>
<style scoped>
.foolter_button {
  position: absolute;
  bottom: 4%;
  right: 34%;
}
.ant-popover-message-title {
  padding: 0px !important;
}
.my-row {
  margin: 10px;
}
</style>
<style lang="less" scoped>

::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}

@media (max-width: 712px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
</style>
