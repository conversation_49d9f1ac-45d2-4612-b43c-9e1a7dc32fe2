<template>
  <a-modal :confirmLoading='confirmLoading' :destroyOnClose="true" :title='title' :visible='visible' :width='800'
    :maskClosable='false' cancelText='关闭' @cancel='handleCancel' @ok='handleOk'>
    <template #footer>
      <a-button key='back' @click='handleCancel'>关闭</a-button>
      <a-button v-show='!disableSubmit' key='submit' :loading='confirmLoading' type='primary' @click='handleOk'>确定
      </a-button>
    </template>
    <a-spin :spinning='confirmLoading'>
      <a-form-model ref='form' :model='model' :rules='validatorRules' :label-col='labelCol' :wrapper-col='wrapperCol'>
        <a-row :gutter='24'>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item hasFeedback label='编码' prop='formKey'>
              <a-input :allow-clear='true' autocomplete='off' :disabled="(disableSubmit || editFlag)"
                v-model='model.formKey' placeholder='请输入表单编码' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item hasFeedback label='名称' prop='formName'>
              <a-input :allow-clear='true' autocomplete='off' :disabled="disableSubmit" v-model='model.formName'
                placeholder='请输入表单名称' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item v-if='dataSourceFlag' hasFeedback label='所属流程'>
              <a-select :getPopupContainer='(node) => node.parentNode' :allow-clear='true' :disabled='disableSubmit'
                v-model='model.modelType' :filter-option='filterOption' option-filter-prop='children'
                placeholder='请选择所属流程' show-search @change='handleChange'>
                <a-select-option v-for='item in dataSource' :key='item.id' :value='item.key'>
                  {{ `${item.name} - ${item.version}` }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    addForm,
    editForm
  } from '@/api/flowable'
  import {
    getAction
  } from '@api/manage'
  import {
    YqFormVerification
  } from '@/mixins/YqFormVerification'

  export default {
    name: 'FormModule',
    mixins: [YqFormVerification],
    data() {
      return {
        title: '操作',
        visible: false,
        disableSubmit: false,
        editFlag: false,
        model: {
          formKey: undefined,
          formName: undefined,
          modelType: undefined
        },
        formItemLayout: {
          md: {
            span: 24
          },
          sm: {
            span: 24
          }
        },
        labelCol: {
          //style: 'width:105px',
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          formKey: [{
              required: true,
              message: '请输入表单编码!'
            },
            {
              min: 1,
              max: 50,
              message: '表单编码应在[1-50]个字符之间!',
              trigger: 'change'
            },
            {
              validator: this.validateCodeFun_1
            }
            /* { pattern: /^([a-zA-Z]([a-zA-Z]+|[0-9]+|[a-zA-Z0-9-_.:]){1,50})$/,
               message: '以字母开头,可包含字母、数字、英文横线或英文句号,1-50个字符'},*/
          ],
          formName: [{
              required: true,
              message: '请输入表单名称!'
            },
            {
              min: 1,
              max: 50,
              message: '表单编码应在[1-50]个字符之间!',
              trigger: 'change'
            }
          ]
        },
        dataSource: [],
        dataSourceFlag: false
      }
    },
    created() {
      this.getFlowData()
    },
    methods: {
      //获取所属流程下拉数据
      getFlowData() {
        getAction('flowable/model/list', {
          pageNo: 1,
          pageSize: 100,
          latestVersion: true
        }).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records || res.result
            this.dataSourceFlag = true
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      //选择所属流程
      handleChange(value) {
        //this.model.modelType = value
      },
      //过滤所属流程下拉数据
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      // 添加表单
      add() {
        this.visible = true
      },
      //编辑表单
      edit(record) {
        this.editFlag = true;
        this.model = record
        console.log(this.model)
        if (this.model.modelType == null) {
          this.model.modelType == undefined
        }
        this.visible = true
      },
      //提交/确认表单数据
      handleOk() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            let obj
            if (!this.model.createTime) {
              obj = addForm(this.model)
            } else {
              console.log(this.model)
              obj = editForm(this.model)
            }
            obj.then((res) => {
              if (res.success) {
                this.dataSourceFlag = false
                that.$message.success(res.message)

                that.handleCancel()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            })
          }
        })
      },
      resetModel() {
        this.model = {
          formKey: undefined,
          formName: undefined,
          modelType: undefined
        }
      },
      handleCancel() {
        this.resetModel()
        this.editFlag = false;
        this.close()
      },
      //关闭函数
      close() {
        this.$emit('ok')
        this.visible = false
        this.dataSourceFlag = false
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/YQNormalModal.less';
</style>