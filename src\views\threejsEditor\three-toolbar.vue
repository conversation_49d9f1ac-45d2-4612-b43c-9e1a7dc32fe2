<template>
  <div style="height: 100%; padding: 0">
    <div v-show="barType === 1" style="height:100%">
      <a-tabs type="card" :activeKey="tabKey" @change="tabChange">
        <a-tab-pane key="1" tab="新增">
          <add></add>
        </a-tab-pane>
        <a-tab-pane key="2" tab="模型">
          <scene-child ref="sceneChild"></scene-child>
        </a-tab-pane>
        <a-tab-pane key="3" tab="场景">
          <scene-info ref="sceneInfo"></scene-info>
        </a-tab-pane>
      </a-tabs>
    </div>
    <div v-show="barType === 2">
      <div style="margin: 0 20px">
        <model-info ref="modelInfo"></model-info>
      </div>
    </div>
  </div>
</template>
<script>
import add from "./modules/add.vue";
import SceneChild from "./modules/scene-child.vue";
import sceneInfo from "./modules/scene-info.vue";
import modelInfo from "./modules/model-info.vue";
export default {
  components: { add, sceneInfo, SceneChild, modelInfo },
  data() {
    return {
      barType: 1,
      targetName: "",
      targetWidth: 0,
      targetHeight: 0,
      targetDepth: 0,
      targetObj: {},
      tabKey: "2",
    };
  },
  created() {
    this.$root.$off("show-obj-params");
    this.$root.$on("show-obj-params", (e) => {
      this.$refs.modelInfo.init(e);
      this.barType = 2;
      this.targetObj = e;
      // if (e.name === "yq_outerMesh") {
      //   this.targetObj = scene.children.find((el) => {
      //     return el.uuid === e.groupId;
      //   });
      // }
      // this.targetName = this.targetObj.userData.name || this.targetObj.name;
      // e.geometry.parameters;
    });
  },
  mounted() {},
  methods: {
    tabChange(e) {
      this.tabKey = e;
      this.getSceneChild();
    },
    getSceneChild() {
      if (this.tabKey == 2 && this.$refs.sceneChild) {
        this.$refs.sceneChild.init();
      }
    },
    goBack() {
      this.barType = 1;
      this.$emit("hideTC");
       this.getSceneChild();
    },
    delGeo() {
      let that = this;
      this.$confirm({
        title: "确定要删除这个实例吗？",
        cancelText: "取消",
        okText: "确定",
        content: "",
        onOk() {
          that.barType = 1;
          that.$emit("delGeo");
          that.getSceneChild();
        },
        onCancel() {},
      });
    },
  },
};
</script>
<style scoped>
.ant-tabs {
  text-align: left;
}
</style>