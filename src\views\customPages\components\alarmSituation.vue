<template>
  <a-card :loading='loading' style='width: 100%; height: 100%'>
    <div slot='title'>
      告警情况
    </div>
    <div class="core-top-core-top-body">
      <div class="core-top-core-top-body-item">
        <img src="../../../assets/alarmRed.png" />
        <div>
          <div class="core-top-core-top-body-item-title">总告警量</div>
          <div class="core-top-core-top-body-item-value" style="color: #D61E1E">
            <animate-number from="0" :to="total_alarm || 0" :key="total_alarm" duration="5000">
            </animate-number>
          </div>
        </div>
      </div>
      <div class="core-top-core-top-body-item">
        <img src="../../../assets/jiejue.png" />
        <div>
          <div class="core-top-core-top-body-item-title"> 解决量</div>
          <div class="core-top-core-top-body-item-value" style="color: #409EFF">
            <animate-number from="0" :to="total_solve || 0" :key="total_solve" duration="5000">
            </animate-number>
          </div>
        </div>
      </div>
      <div class="core-top-core-top-body-item">
        <img src="../../../assets/date.png" />
        <div>
          <div class="core-top-core-top-body-item-title">今日解决量</div>
          <div class="core-top-core-top-body-item-value" style="color: #FFC954">
            <animate-number from="0" :to="today_solve || 0" :key="today_solve" duration="5000">
            </animate-number>
          </div>
        </div>
      </div>
    </div>
    <div class="core-top-core-top-body">
      <div class="core-top-core-top-body-item">
        <img src="../../../assets/alarmRed.png" />
        <div>
          <div class="core-top-core-top-body-item-title">今日告警量</div>
          <div class="core-top-core-top-body-item-value" style="color: #D61E1E">
            <animate-number from="0" :to="today_alarm || 0" :key="today_alarm" duration="5000">
            </animate-number>
          </div>
        </div>
      </div>
      <div class="core-top-core-top-body-item">
        <img src="../../../assets/jiejue.png" />
        <div>
          <div class="core-top-core-top-body-item-title">解决占比</div>
          <div class="core-top-core-top-body-item-value" style="color: #409EFF">
            <animate-number from="0" :to="total_solve_percentage || 0" :key="total_solve_percentage" duration="5000">
            </animate-number>
          </div>
        </div>
      </div>
      <div class="core-top-core-top-body-item">
        <img src="../../../assets/date.png" />
        <div>
          <div class="core-top-core-top-body-item-title">今日解决占比</div>
          <div class="core-top-core-top-body-item-value" style="color: #FFC954">
            <animate-number from="0" :to="today_solve_percentage || 0" :key="today_solve_percentage" duration="5000">
            </animate-number>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>
<script>
  import {
    getAction
  } from '@api/manage'

  export default {
    name: 'alarmSituation',
    data() {
      return {
        total_solve_percentage: 0,
        total_solve: 0,
        total_alarm: 0,
        today_solve_percentage: 0,
        today_solve: 0,
        today_alarm: 0,
        loading: false,
      }
    },
    created() {
      this.getCount();
    },
    mounted() {},
    methods: {
      getCount() {
        getAction('/data-analysis/alarm/overview').then((res) => {
          if (res.code == 200) {
            this.today_alarm = res.result && res.result.today_alarm ? res.result.today_alarm : 0
            this.today_solve = res.result && res.result.today_solve ? res.result.today_solve : 0
            this.today_solve_percentage = res.result && res.result.today_solve_percentage ?
              res.result.today_solve_percentage : 0
            this.total_alarm = res.result && res.result.total_alarm ? res.result.total_alarm : 0
            this.total_solve = res.result && res.result.total_solve ? res.result.total_solve : 0
            this.total_solve_percentage = res.result && res.result.total_solve_percentage ?
              res.result.total_solve_percentage : 0
          }
        })
      },
    }
  }
</script>


<style scoped lang='less'>
  .core-top-core-top-body {
    width: 100%;
    height: 85%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #000;
    font-size: 0.2rem
      /* 16/80 */
    ;
    padding: 0.45rem
      /* 24/80 */
    ;

    .core-top-core-top-body-item {
      width: 33%;
      height: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;

      .core-top-core-top-body-item-title {
        display: flex;
        width: 100%;
        height: 20%;
        padding-left: 0.25rem;
      }

      .core-top-core-top-body-item-value {
        width: 100%;
        height: 80%;
        display: flex;
        align-items: center;
        padding-left: 0.25rem;
        color: #000;
        font-size: 0.45rem;
        font-family: 'zhenku';
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .header-right-time {
      font-size: 0.175rem;
      font-family: PingFang SC;
      letter-spacing: 0px;
      font-weight: 100;
      color: #ffffff;
      display: flex;
      align-items: center;

      .time-range-div {
        margin-right: 0.4375rem;
        margin-left: 0.2rem;
      }
    }
  }
</style>