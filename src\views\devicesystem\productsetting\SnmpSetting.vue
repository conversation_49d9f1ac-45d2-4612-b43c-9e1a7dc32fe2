<template>
  <div>
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24"> </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px; ">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 14px; " />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px; ">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEditOid(record)">编辑</a>
        </span>
      </a-table>
    </div>

    <proerty-metadata-modal ref="modalForm" @ok="modalFormOk"></proerty-metadata-modal>

    <a-modal
      title="设置OID唯一标识"
      :visible="oidVisible"
      :confirm-loading="confirmLoadingOid"
      @ok="handleOkOid"
      @cancel="handleCancelOid"
    >
      <a-input v-modal="oid" placeholder="请输入OID"></a-input>
      <p>{{ ModalText }}</p>
    </a-modal>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixinNoInit'
import ProertyMetadataModal from '../modules/ProertyMetadataModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { httpAction, getAction } from '@/api/manage'
export default {
  name: 'SnmpSetting',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    ProertyMetadataModal,
    JSuperQuery
  },
  data() {
    return {
      description: '属性元数据管理页面',
      oid: '',
      proertyMetadataId: '',
      oidVisible: false,
      confirmLoadingOid: false,
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '属性标识',
          align: 'center',
          dataIndex: 'code'
        },
        {
          title: '属性名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '数据类型',
          align: 'center',
          dataIndex: 'dataType'
        },
        {
          title: 'OID唯一标识',
          align: 'center',
          dataIndex: 'oid'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/product/proertyMetadata/list',
        delete: '/product/proertyMetadata/delete',
        deleteBatch: '/product/proertyMetadata/deleteBatch',
        exportXlsUrl: '/product/proertyMetadata/exportXls',
        importExcelUrl: 'product/proertyMetadata/importExcel'
      },
      dictOptions: {},
      superFieldList: []
    }
  },
  created() {
    this.getSuperFieldList()
    this.queryParam.productId = window.localStorage.pruductId
    this.loadData()
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'code', text: '属性标识', dictCode: '' })
      fieldList.push({ type: 'string', value: 'name', text: '属性名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dataType', text: '数据类型', dictCode: '' })
      fieldList.push({ type: 'Text', value: 'formatValue', text: '格式化值', dictCode: '' })
      fieldList.push({ type: 'string', value: 'readWriteFlag', text: '读写标识', dictCode: '' })
      fieldList.push({ type: 'string', value: 'remark', text: '描述', dictCode: '' })
      this.superFieldList = fieldList
    },
    handleEditOid(record) {
      this.oidVisible = true
      this.proertyMetadataId = record.id
    },
    handleOkOid(record) {
      this.oidVisible = false
    },
    handleCancelOid(record) {
      this.oidVisible = false
    },
    setSnmp() {
      getAction('/product/proertyMetadata/setSnmp').then(res => {})
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
